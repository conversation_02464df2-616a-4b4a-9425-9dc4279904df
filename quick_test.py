#!/usr/bin/env python3
import requests
import json

try:
    response = requests.post('http://localhost:8080/api/get-patent', 
                           headers={'Content-Type': 'application/json'},
                           json={'patent_number': 'US11228184B2'})
    
    if response.ok:
        data = response.json()
        print(f"✅ Patent data retrieved successfully")
        print(f"   Patent number: {data.get('patent_number')}")
        print(f"   Has data: {data.get('has_data')}")
        print(f"   Analysis data length: {len(data.get('analysis_data', ''))}")
        print(f"   Infringed models: {len(data.get('infringed_models', []))}")
        
        # Check if analysis data contains the patent number
        analysis_data = data.get('analysis_data', '')
        if 'US11228184B2' in analysis_data:
            print("✅ Analysis data contains correct patent number")
        else:
            print("❌ Analysis data missing patent number")
            
    else:
        print(f"❌ Failed: {response.status_code}")
        print(response.text)
        
except Exception as e:
    print(f"❌ Error: {e}")
