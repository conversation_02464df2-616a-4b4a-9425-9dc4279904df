import json
import os
import sqlite3
from typing import Dict, Any, Optional, List
import logging
from google.adk.tools import FunctionTool

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database file path
DB_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), "wissen_core.db")

def init_db():
    """Initialize the database with required tables if they don't exist"""
    conn = sqlite3.connect(DB_FILE)
    cursor = conn.cursor()
    
    # Create patent_analysis table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS patent_analysis (
        patent_number TEXT PRIMARY KEY,
        analysis_data TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # Create patent_novelty table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS patent_novelty (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        patent_number TEXT UNIQUE NOT NULL,
        title TEXT,
        priority_date DATETIME,
        assignees TEXT,
        competitors TEXT,
        novelty_summary TEXT,
        inserted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME
    )
    ''')
    
    # Create infringed_models table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS infringed_models (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        patent_number TEXT NOT NULL,
        priority_date DATETIME,
        model TEXT NOT NULL,
        launched_date TEXT,
        processed_launched_date DATETIME,
        link TEXT,
        inserted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME,
        is_relevant INTEGER,
        relevance_reason TEXT,
        UNIQUE(patent_number, model)
    )
    ''')
    
    # Create claim_charts table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS claim_charts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        infringed_model_id INTEGER NOT NULL,
        claim_element TEXT NOT NULL,
        corresponding_feature TEXT,
        source_justification TEXT,
        infringement_risk TEXT,
        risk_justification TEXT,
        inserted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (infringed_model_id) REFERENCES infringed_models(id)
    )
    ''')
    
    conn.commit()
    conn.close()
    logger.info(f"Database initialized at {DB_FILE}")

def save_patent_analysis(patent_number: str, analysis_data: Dict[str, Any]) -> bool:
    """
    Save patent analysis data to the database
    
    Args:
        patent_number: Patent number
        analysis_data: Patent analysis data
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Initialize database if needed
        if not os.path.exists(DB_FILE):
            init_db()
        
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Convert analysis data to JSON string
        analysis_json = json.dumps(analysis_data)
        
        # Insert or replace existing data
        cursor.execute(
            "INSERT OR REPLACE INTO patent_analysis (patent_number, analysis_data) VALUES (?, ?)",
            (patent_number, analysis_json)
        )
        
        conn.commit()
        conn.close()
        
        logger.info(f"Patent analysis saved for {patent_number}")
        return True
    except Exception as e:
        logger.error(f"Error saving patent analysis: {e}")
        return False

def get_patent_analysis(patent_number: str) -> Optional[Dict[str, Any]]:
    """
    Get patent analysis data from the database
    
    Args:
        patent_number: Patent number
        
    Returns:
        Patent analysis data or None if not found
    """
    try:
        # Check if database exists
        if not os.path.exists(DB_FILE):
            return None
        
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Query the database
        cursor.execute(
            "SELECT analysis_data FROM patent_analysis WHERE patent_number = ?",
            (patent_number,)
        )
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            # Parse JSON string to dictionary
            return json.loads(result[0])
        
        return None
    except Exception as e:
        logger.error(f"Error retrieving patent analysis: {e}")
        return None

def check_patent_exists(patent_number: str) -> bool:
    """
    Check if a patent analysis exists in the database
    
    Args:
        patent_number: Patent number
        
    Returns:
        True if exists, False otherwise
    """
    try:
        # Check if database exists
        if not os.path.exists(DB_FILE):
            return False
        
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Query the database
        cursor.execute(
            "SELECT 1 FROM patent_analysis WHERE patent_number = ?",
            (patent_number,)
        )
        
        result = cursor.fetchone()
        conn.close()
        
        return result is not None
    except Exception as e:
        logger.error(f"Error checking patent existence: {e}")
        return False

def save_patent_novelty(patent_number: str, novelty_data: Dict[str, Any]) -> bool:
    """
    Save patent novelty data to the database
    
    Args:
        patent_number: Patent number
        novelty_data: Patent novelty data including title, priority_date, assignees, competitors, novelty_summary
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Initialize database if needed
        if not os.path.exists(DB_FILE):
            init_db()
        
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Check if record exists
        cursor.execute(
            "SELECT 1 FROM patent_novelty WHERE patent_number = ?",
            (patent_number,)
        )
        
        if cursor.fetchone():
            # Update existing record
            cursor.execute(
                """
                UPDATE patent_novelty 
                SET title = ?, priority_date = ?, assignees = ?, competitors = ?, 
                    novelty_summary = ?, updated_at = CURRENT_TIMESTAMP
                WHERE patent_number = ?
                """,
                (
                    novelty_data.get('title', ''),
                    novelty_data.get('priority_date'),
                    json.dumps(novelty_data.get('assignees', [])),
                    json.dumps(novelty_data.get('competitors', [])),
                    novelty_data.get('novelty_summary', ''),
                    patent_number
                )
            )
        else:
            # Insert new record
            cursor.execute(
                """
                INSERT INTO patent_novelty 
                (patent_number, title, priority_date, assignees, competitors, novelty_summary)
                VALUES (?, ?, ?, ?, ?, ?)
                """,
                (
                    patent_number,
                    novelty_data.get('title', ''),
                    novelty_data.get('priority_date'),
                    json.dumps(novelty_data.get('assignees', [])),
                    json.dumps(novelty_data.get('competitors', [])),
                    novelty_data.get('novelty_summary', '')
                )
            )
        
        conn.commit()
        conn.close()
        
        logger.info(f"Patent novelty saved for {patent_number}")
        return True
    except Exception as e:
        logger.error(f"Error saving patent novelty: {e}")
        return False

def get_patent_novelty(patent_number: str) -> Optional[Dict[str, Any]]:
    """
    Get patent novelty data from the database
    
    Args:
        patent_number: Patent number
        
    Returns:
        Patent novelty data or None if not found
    """
    try:
        # Check if database exists
        if not os.path.exists(DB_FILE):
            return None
        
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Query the database
        cursor.execute(
            """
            SELECT title, priority_date, assignees, competitors, novelty_summary
            FROM patent_novelty WHERE patent_number = ?
            """,
            (patent_number,)
        )
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                "title": result[0],
                "priority_date": result[1],
                "assignees": json.loads(result[2]) if result[2] else [],
                "competitors": json.loads(result[3]) if result[3] else [],
                "novelty_summary": result[4]
            }
        
        return None
    except Exception as e:
        logger.error(f"Error retrieving patent novelty: {e}")
        return None

def save_infringed_model(patent_number: str, model_data: Dict[str, Any]) -> Optional[int]:
    """
    Save infringed model data to the database
    
    Args:
        patent_number: Patent number
        model_data: Model data including model name, launch date, link, etc.
        
    Returns:
        Model ID if successful, None otherwise
    """
    try:
        # Initialize database if needed
        if not os.path.exists(DB_FILE):
            init_db()
        
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Check if record exists
        cursor.execute(
            "SELECT id FROM infringed_models WHERE patent_number = ? AND model = ?",
            (patent_number, model_data.get('model', ''))
        )
        
        result = cursor.fetchone()
        
        if result:
            # Update existing record
            model_id = result[0]
            cursor.execute(
                """
                UPDATE infringed_models 
                SET priority_date = ?, launched_date = ?, processed_launched_date = ?,
                    link = ?, updated_at = CURRENT_TIMESTAMP,
                    is_relevant = ?, relevance_reason = ?
                WHERE id = ?
                """,
                (
                    model_data.get('priority_date'),
                    model_data.get('launched_date'),
                    model_data.get('processed_launched_date'),
                    model_data.get('link', ''),
                    model_data.get('is_relevant'),
                    model_data.get('relevance_reason', ''),
                    model_id
                )
            )
        else:
            # Insert new record
            cursor.execute(
                """
                INSERT INTO infringed_models 
                (patent_number, priority_date, model, launched_date, processed_launched_date,
                 link, is_relevant, relevance_reason)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """,
                (
                    patent_number,
                    model_data.get('priority_date'),
                    model_data.get('model', ''),
                    model_data.get('launched_date'),
                    model_data.get('processed_launched_date'),
                    model_data.get('link', ''),
                    model_data.get('is_relevant'),
                    model_data.get('relevance_reason', '')
                )
            )
            model_id = cursor.lastrowid
        
        conn.commit()
        conn.close()
        
        logger.info(f"Infringed model saved for {patent_number}: {model_data.get('model', '')}")
        return model_id
    except Exception as e:
        logger.error(f"Error saving infringed model: {e}")
        return None

def save_claim_chart(model_id: int, claim_chart_data: List[Dict[str, Any]]) -> bool:
    """
    Save claim chart data to the database
    
    Args:
        model_id: Infringed model ID
        claim_chart_data: List of claim chart elements
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Initialize database if needed
        if not os.path.exists(DB_FILE):
            init_db()
        
        conn = sqlite3.connect(DB_FILE)
        cursor = conn.cursor()
        
        # Delete existing claim charts for this model
        cursor.execute("DELETE FROM claim_charts WHERE infringed_model_id = ?", (model_id,))
        
        # Insert new claim charts
        for claim in claim_chart_data:
            cursor.execute(
                """
                INSERT INTO claim_charts 
                (infringed_model_id, claim_element, corresponding_feature, 
                 source_justification, infringement_risk, risk_justification)
                VALUES (?, ?, ?, ?, ?, ?)
                """,
                (
                    model_id,
                    claim.get('claim_element', ''),
                    claim.get('corresponding_feature', ''),
                    claim.get('source_justification', ''),
                    claim.get('infringement_risk', ''),
                    claim.get('risk_justification', '')
                )
            )
        
        conn.commit()
        conn.close()
        
        logger.info(f"Claim charts saved for model ID {model_id}")
        return True
    except Exception as e:
        logger.error(f"Error saving claim charts: {e}")
        return False

def get_infringed_models(patent_number: str) -> List[Dict[str, Any]]:
    """
    Get infringed models for a patent from the database
    
    Args:
        patent_number: Patent number
        
    Returns:
        List of infringed models with their claim charts
    """
    try:
        # Check if database exists
        if not os.path.exists(DB_FILE):
            return []
        
        conn = sqlite3.connect(DB_FILE)
        conn.row_factory = sqlite3.Row  # This enables column access by name
        cursor = conn.cursor()
        
        # Query the database for models
        cursor.execute(
            """
            SELECT id, model, priority_date, launched_date, processed_launched_date,
                   link, is_relevant, relevance_reason
            FROM infringed_models WHERE patent_number = ?
            """,
            (patent_number,)
        )
        
        models = []
        for row in cursor.fetchall():
            model = dict(row)
            model_id = model['id']
            
            # Get claim charts for this model
            claim_cursor = conn.cursor()
            claim_cursor.execute(
                """
                SELECT claim_element, corresponding_feature, source_justification,
                       infringement_risk, risk_justification
                FROM claim_charts WHERE infringed_model_id = ?
                """,
                (model_id,)
            )
            
            claim_charts = []
            for claim_row in claim_cursor.fetchall():
                claim_charts.append(dict(claim_row))
            
            model['claim_charts'] = claim_charts
            models.append(model)
        
        conn.close()
        return models
    except Exception as e:
        logger.error(f"Error retrieving infringed models: {e}")
        return []

def get_complete_patent_analysis(patent_number: str) -> Dict[str, Any]:
    """
    Get complete patent analysis including novelty and infringed models

    Args:
        patent_number: Patent number

    Returns:
        Complete patent analysis data with properly formatted content
    """
    logger.info(f"Getting complete analysis for patent: {patent_number}")

    # Get patent analysis from original table
    analysis_data = get_patent_analysis(patent_number) or {}
    logger.info(f"Analysis data retrieved: {bool(analysis_data)} - {type(analysis_data)}")

    # Get patent novelty
    novelty_data = get_patent_novelty(patent_number) or {}
    logger.info(f"Novelty data retrieved: {bool(novelty_data)} - {type(novelty_data)}")

    # Get infringed models with claim charts
    models_data = get_infringed_models(patent_number) or []
    logger.info(f"Models data retrieved: {len(models_data)} items")

    # Reconstruct the analysis content from the chunked data
    reconstructed_analysis = reconstruct_analysis_content(analysis_data, novelty_data, models_data, patent_number)
    logger.info(f"Reconstructed analysis length: {len(reconstructed_analysis) if reconstructed_analysis else 0}")

    # Determine if we have meaningful data
    analysis_check = bool(analysis_data and any(analysis_data.values()))
    novelty_check = bool(novelty_data and any(v for v in novelty_data.values() if v))
    models_check = bool(models_data and len(models_data) > 0)
    reconstructed_check = bool(reconstructed_analysis and len(reconstructed_analysis.strip()) > 0)

    has_meaningful_data = analysis_check or novelty_check or models_check or reconstructed_check

    logger.info(f"Has meaningful data: {has_meaningful_data}")
    logger.info(f"  - Analysis data check: {analysis_check}")
    logger.info(f"  - Novelty data check: {novelty_check}")
    logger.info(f"  - Models data check: {models_check}")
    logger.info(f"  - Reconstructed analysis check: {reconstructed_check}")

    # Combine all data
    complete_data = {
        "patent_number": patent_number,
        "analysis_data": reconstructed_analysis,
        "novelty_data": novelty_data,
        "infringed_models": models_data,
        "has_data": has_meaningful_data
    }

    return complete_data

def reconstruct_analysis_content(analysis_data: Dict[str, Any], novelty_data: Dict[str, Any],
                               models_data: List[Dict[str, Any]], patent_number: str) -> str:
    """
    Reconstruct the analysis content from chunked database data

    Args:
        analysis_data: Raw analysis data from database
        novelty_data: Novelty data from database
        models_data: Infringed models data with claim charts
        patent_number: Patent number

    Returns:
        Formatted analysis content string
    """
    # Check if we have any meaningful data to reconstruct
    has_meaningful_data = (
        (analysis_data and any(analysis_data.values())) or
        (novelty_data and any(v for v in novelty_data.values() if v)) or
        (models_data and len(models_data) > 0)
    )

    if not has_meaningful_data:
        # Return empty string to indicate no data available
        return ""

    content_parts = []

    # Add executive summary
    content_parts.append(f"# Patent Analysis Summary: {patent_number}\n")
    content_parts.append(f"Analysis retrieved from database on: {get_current_timestamp()}\n")
    content_parts.append(f"Patent Number: {patent_number}\n\n")

    # Add novelty analysis if available
    if novelty_data:
        content_parts.append("## Patent Details\n")
        if novelty_data.get('title'):
            content_parts.append(f"**Title:** {novelty_data['title']}\n")
        if novelty_data.get('priority_date'):
            content_parts.append(f"**Priority Date:** {novelty_data['priority_date']}\n")
        if novelty_data.get('assignees'):
            assignees = novelty_data['assignees'] if isinstance(novelty_data['assignees'], list) else []
            if assignees:
                content_parts.append(f"**Assignees:** {', '.join(assignees)}\n")

        if novelty_data.get('novelty_summary'):
            content_parts.append(f"\n**Novelty Summary:**\n{novelty_data['novelty_summary']}\n\n")

    # Add potentially infringing products if available
    if models_data:
        content_parts.append("## Potentially Infringing Products\n\n")
        content_parts.append("| Company | Model | Launch Date | Risk Assessment | Risk Justification |\n")
        content_parts.append("| --- | --- | --- | --- | --- |\n")

        for model in models_data:
            company = "Not specified"
            model_name = model.get('model', 'Unknown Model')
            launch_date = model.get('launched_date', 'Not specified')

            # Determine risk assessment from claim charts
            risk_assessment = "Not assessed"
            risk_justification = "No analysis available"

            if model.get('claim_charts'):
                # Analyze claim charts to determine risk
                high_risk_count = sum(1 for chart in model['claim_charts']
                                    if chart.get('infringement_risk', '').lower() == 'high')
                medium_risk_count = sum(1 for chart in model['claim_charts']
                                      if chart.get('infringement_risk', '').lower() == 'medium')

                if high_risk_count > 0:
                    risk_assessment = "High"
                    risk_justification = f"{high_risk_count} high-risk claim elements identified"
                elif medium_risk_count > 0:
                    risk_assessment = "Medium"
                    risk_justification = f"{medium_risk_count} medium-risk claim elements identified"
                else:
                    risk_assessment = "Low"
                    risk_justification = "No significant infringement risk identified"

            content_parts.append(f"| {company} | {model_name} | {launch_date} | {risk_assessment} | {risk_justification} |\n")

        content_parts.append("\n")

    # Add detailed claim charts if available
    if models_data and any(model.get('claim_charts') for model in models_data):
        content_parts.append("## Detailed Claim Charts\n\n")

        for model in models_data:
            if model.get('claim_charts'):
                content_parts.append(f"### {model.get('model', 'Unknown Model')}\n\n")
                content_parts.append("| Claim Element | Corresponding Feature | Source Justification |\n")
                content_parts.append("| --- | --- | --- |\n")

                for chart in model['claim_charts']:
                    claim_element = chart.get('claim_element', 'Not specified')
                    corresponding_feature = chart.get('corresponding_feature', 'Not specified')
                    source_justification = chart.get('source_justification', 'Not provided')

                    content_parts.append(f"| {claim_element} | {corresponding_feature} | {source_justification} |\n")

                content_parts.append("\n")

    # Add original analysis data if available
    if analysis_data:
        content_parts.append("## Additional Analysis Data\n\n")

        # Extract specific fields from analysis_data
        if isinstance(analysis_data, dict):
            if analysis_data.get('competition_search_output'):
                content_parts.append("### Competition Analysis\n")
                content_parts.append(f"{analysis_data['competition_search_output']}\n\n")

            if analysis_data.get('novelty_analysis'):
                content_parts.append("### Novelty Analysis\n")
                content_parts.append(f"{analysis_data['novelty_analysis']}\n\n")

    return "".join(content_parts)

def get_current_timestamp() -> str:
    """Get current timestamp in a readable format"""
    from datetime import datetime
    return datetime.now().strftime("%m/%d/%Y, %I:%M:%S %p")

# Create function tools for use in the agent
save_patent_tool = FunctionTool(save_patent_analysis)
get_patent_tool = FunctionTool(get_patent_analysis)
check_patent_tool = FunctionTool(check_patent_exists)

# Add new function tools for the additional database functions
save_novelty_tool = FunctionTool(save_patent_novelty)
get_novelty_tool = FunctionTool(get_patent_novelty)
save_model_tool = FunctionTool(save_infringed_model)
save_claim_chart_tool = FunctionTool(save_claim_chart)
get_complete_analysis_tool = FunctionTool(get_complete_patent_analysis)
