// Global variables
const API_BASE_URL = 'http://localhost:8000';
let currentSessionId = null;
let currentUserId = 'user-' + Date.now();
const APP_NAME = 'infringement-agent';
let patentQueue = [];
let activePatentAnalyses = 0;
const MAX_CONCURRENT_ANALYSES = 8;

// DOM elements
const patentInput = document.getElementById('patent-input');
const analyzeBtn = document.getElementById('analyze-btn');
const loadingIndicator = document.getElementById('loading-indicator');
const resultsContainer = document.getElementById('results-container');
const alertContainer = document.getElementById('alert-container');
const confirmationDialog = document.getElementById('confirmation-dialog');
const confirmationBackdrop = document.getElementById('confirmation-backdrop');
const confirmationMessage = document.getElementById('confirmation-message');
const confirmYesBtn = document.getElementById('confirm-yes');
const confirmNoBtn = document.getElementById('confirm-no');

// Tab content elements
const summaryContent = document.getElementById('summary-content');
const noveltyContent = document.getElementById('novelty-content');
const productsContent = document.getElementById('products-content');
const claimChartContent = document.getElementById('claim-chart-content');

// Additional DOM elements
const multiPatentContainer = document.getElementById('multi-patent-container');
const toggleMultiPatentBtn = document.getElementById('toggle-multi-patent');
const patentListInput = document.getElementById('patent-list-input');
const analyzeMultiBtn = document.getElementById('analyze-multi-btn');
const patentFamilyCheck = document.getElementById('patent-family-check');
const patentList = document.getElementById('patent-list');
const patentItemsContainer = document.getElementById('patent-items-container');
const downloadAllBtn = document.getElementById('download-all-btn');
const downloadBtn = document.getElementById('download-btn');
const debugDbBtn = document.getElementById('debug-db-btn');
const customPrompt = document.getElementById('custom-prompt');

// Multi-patent results elements
const multiPatentResults = document.getElementById('multi-patent-results');
const combinedProductsContent = document.getElementById('combined-products-content');
const combinedClaimChartsContent = document.getElementById('combined-claim-charts-content');

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM Content Loaded - Setting up event listeners');

  // Set up event listeners
  analyzeBtn.addEventListener('click', startAnalysis);
  patentInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') startAnalysis();
  });

  // Set up confirmation dialog buttons
  confirmYesBtn.addEventListener('click', () => {
    hideConfirmationDialog();
    proceedWithNewAnalysis();
  });

  confirmNoBtn.addEventListener('click', () => {
    hideConfirmationDialog();
    useExistingAnalysis();
  });

  // Close dialog when clicking backdrop
  confirmationBackdrop.addEventListener('click', () => {
    hideConfirmationDialog();
  });

  // Set up download button - Modified to download Excel from server
  const downloadBtn = document.getElementById('download-btn');
  if (downloadBtn) {
    downloadBtn.addEventListener('click', downloadExcelReport);
  }

  // Set up multi-patent toggle
  toggleMultiPatentBtn.addEventListener('click', toggleMultiPatentMode);

  // Set up multi-patent analyze button
  analyzeMultiBtn.addEventListener('click', startMultiPatentAnalysis);

  // Set up download all button
  downloadAllBtn.addEventListener('click', downloadAllReports);

  // Set up debug database button
  debugDbBtn.addEventListener('click', debugDatabase);

  // Add loading indicator text
  const loadingText = document.createElement('p');
  loadingText.id = 'loading-text';
  loadingText.className = 'mt-2 mb-0';
  loadingText.textContent = 'Analyzing patent. This may take a few minutes...';
  loadingIndicator.appendChild(loadingText);
});

// Download Excel report from server
async function downloadExcelReport() {
  const patentNumber = patentInput.value.trim();

  if (!patentNumber) {
    showAlert('No patent analysis to download', 'warning');
    return;
  }

  try {
    showAlert('Downloading Excel report...', 'info');
    
    // Request the Excel file from the server
    const response = await fetch('http://localhost:8080/download-report', {
      method: 'GET',
    });

    if (!response.ok) {
      throw new Error(`Failed to download report: ${response.status}`);
    }

    // Get the blob and create download link
    const blob = await response.blob();
    const url = URL.createObjectURL(blob);
    
    // Extract filename from response headers or use default
    const contentDisposition = response.headers.get('Content-Disposition');
    let filename = `patent-analysis-${patentNumber.replace(/[^\w\d]/g, '_')}-${Date.now()}.xlsx`;
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }

    // Create download link and trigger download
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();

    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showAlert(`Excel report downloaded: ${filename}`, 'success');

  } catch (error) {
    console.error('Error downloading Excel report:', error);
    showAlert(`Failed to download Excel report: ${error.message}. Falling back to HTML report.`, 'warning');
    // Fallback to HTML download
    downloadHTMLReport();
  }
}

// Fallback HTML report download
function downloadHTMLReport() {
  const patentNumber = patentInput.value.trim();
  
  const reportContent = `# Patent Infringement Analysis Report

## Patent: ${patentNumber}
### Generated on: ${new Date().toLocaleString()}

---

## Summary
${summaryContent.innerText || 'No summary available'}

---

*Report generated by Patent Infringement Analyzer*
*Timestamp: ${new Date().toISOString()}*
`;

  const blob = new Blob([reportContent], { type: 'text/markdown;charset=utf-8' });
  const url = URL.createObjectURL(blob);

  const a = document.createElement('a');
  a.href = url;
  a.download = `patent-analysis-${patentNumber.replace(/[^\w\d]/g, '_')}-${Date.now()}.md`;
  document.body.appendChild(a);
  a.click();

  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  showAlert(`Markdown report downloaded for patent ${patentNumber}`, 'success');
}

// Toggle multi-patent mode
function toggleMultiPatentMode() {
  multiPatentContainer.classList.toggle('d-none');
  if (!multiPatentContainer.classList.contains('d-none')) {
    toggleMultiPatentBtn.textContent = 'Switch to Single Patent Mode';
  } else {
    toggleMultiPatentBtn.textContent = 'Switch to Multi-Patent Mode';
  }
}

// Start multi-patent analysis
function startMultiPatentAnalysis() {
  const patentNumbers = patentListInput.value.trim().split('\n').filter(p => p.trim() !== '');

  if (patentNumbers.length === 0) {
    showAlert('Please enter at least one patent number', 'danger');
    return;
  }

  // Clear previous patent list
  patentItemsContainer.innerHTML = '';
  patentQueue = [];

  // Show patent list
  patentList.classList.remove('d-none');

  // Add patents to queue
  patentNumbers.forEach(patentNumber => {
    patentNumber = patentNumber.trim();
    if (patentNumber) {
      addPatentToQueue(patentNumber);
    }
  });

  // Start processing queue
  processPatentQueue();
}

// Add patent to queue
function addPatentToQueue(patentNumber) {
  const patentId = 'patent-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);

  // Create patent item in UI
  const patentItem = document.createElement('div');
  patentItem.id = patentId;
  patentItem.className = 'patent-item';
  patentItem.innerHTML = `
    <div>
      <span class="patent-number">${patentNumber}</span>
    </div>
    <div>
      <span class="status status-pending">Pending</span>
    </div>
  `;
  patentItemsContainer.appendChild(patentItem);

  // Add to queue
  patentQueue.push({
    id: patentId,
    patentNumber: patentNumber,
    status: 'pending',
    result: null
  });
}

// Process patent queue
async function processPatentQueue() {
  // Check if we can process more patents
  while (activePatentAnalyses < MAX_CONCURRENT_ANALYSES && patentQueue.some(p => p.status === 'pending')) {
    // Get next pending patent
    const nextPatent = patentQueue.find(p => p.status === 'pending');
    if (nextPatent) {
      // Update status
      nextPatent.status = 'processing';
      updatePatentStatus(nextPatent.id, 'processing');

      // Increment active analyses
      activePatentAnalyses++;

      // Start analysis (don't await to allow concurrent processing)
      analyzePatent(nextPatent).finally(() => {
        // Decrement active analyses when done
        activePatentAnalyses--;
        // Continue processing queue
        processPatentQueue();
      });
    }
  }

  // Check if all patents are completed
  if (patentQueue.every(p => p.status === 'completed' || p.status === 'error')) {
    downloadAllBtn.disabled = false;
    updateMultiPatentResults();
  }
}

// Analyze a single patent from the queue
async function analyzePatent(patent) {
  try {
    // First check if patent exists in database
    const existingData = await checkPatentInDatabase(patent.patentNumber);

    if (existingData) {
      console.log(`Patent ${patent.patentNumber} found in database, loading existing data...`);

      // Load the complete patent data from database
      const patentData = await loadPatentFromDatabase(patent.patentNumber);

      if (patentData) {
        // Store the database result
        patent.result = [{
          content: {
            parts: [{
              text: `Patent ${patent.patentNumber} loaded from database with existing analysis.`
            }]
          },
          actions: {
            state_delta: {
              infringement_agent_output: patentData.analysis_data || 'Analysis data loaded from database',
              database_data: patentData
            }
          }
        }];
        patent.status = 'completed';
        patent.fromDatabase = true;

        // Update UI
        updatePatentStatus(patent.id, 'completed');
        return;
      }
    }

    // If not in database, proceed with new analysis
    console.log(`Patent ${patent.patentNumber} not found in database, starting new analysis...`);

    // Create a new session
    const sessionId = 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    await createSessionForPatent(sessionId);

    // Get custom prompt if any
    const customInstructions = customPrompt.value.trim();
    let message = `Please analyze patent ${patent.patentNumber} for potential infringement.

CRITICAL REQUIREMENTS:
1. Generate Excel claim charts report in generated_reports directory using the claim_chart_to_excel tool
2. Save all analysis data to the database using the appropriate database tools (save_patent_analysis, save_patent_novelty, save_claim_charts)
3. DO NOT include any "Analysis Status" section in your output
4. DO NOT repeat content or sections - provide each piece of information only once
5. DO NOT include status indicators like "✅ Complete" or "⏳ Pending"
6. Ensure proper table formatting for all outputs

Please structure your response with the following sections (ONLY ONCE EACH):

1. **Executive Summary**: Brief overview of findings
2. **Potentially Infringing Products**: Present as a well-formatted table with columns: Company, Model, Category, Launch Date, Risk Assessment, Risk Justification
3. **Detailed Claim Charts**: Present as structured tables with columns: Claim Element, Corresponding Feature in Product, Source Justification
4. **Novelty Analysis**: Include prior art search and novelty assessment

Use consistent table formatting with proper markdown tables. Ensure all risk assessments are clearly categorized as High, Medium, or Low. Do deep research and provide comprehensive analysis.

CRITICAL:
- Use the claim_chart_to_excel tool to generate the Excel report file in the generated_reports directory
- Save all analysis data to the database for future retrieval using the database tools
- Provide content only once - no repetition of sections or information
- Keep output concise and well-organized`;

    if (customInstructions) {
      message += `. ${customInstructions}`;
    }

    // Send the analysis request
    const response = await sendMessageForPatent(sessionId, message);

    // Store result
    patent.result = response;
    patent.sessionId = sessionId;
    patent.status = 'completed';

    // Update UI
    updatePatentStatus(patent.id, 'completed');

  } catch (error) {
    console.error(`Error analyzing patent ${patent.patentNumber}:`, error);
    patent.status = 'error';
    patent.error = error.message;
    updatePatentStatus(patent.id, 'error');
  }
}

// Update patent status in UI
function updatePatentStatus(patentId, status) {
  const patentItem = document.getElementById(patentId);
  if (patentItem) {
    const statusSpan = patentItem.querySelector('.status');
    statusSpan.className = `status status-${status}`;
    statusSpan.textContent = status.charAt(0).toUpperCase() + status.slice(1);
  }
}

// Create a session for a specific patent
async function createSessionForPatent(sessionId) {
  try {
    console.log('Creating session:', sessionId);

    const response = await fetch(`${API_BASE_URL}/apps/${APP_NAME}/users/${currentUserId}/sessions/${sessionId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Session creation error:', errorText);
      throw new Error(`Failed to create session: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ Session created:', data);
    return data;
  } catch (error) {
    console.error('Error creating session:', error);
    throw new Error(`Session creation failed: ${error.message}`);
  }
}

// Send a message for a specific patent
async function sendMessageForPatent(sessionId, message) {
  try {
    console.log('Sending message:', message);

    const response = await fetch(`${API_BASE_URL}/run`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        app_name: APP_NAME,
        user_id: currentUserId,
        session_id: sessionId,
        new_message: {
          parts: [{ text: message }],
          role: 'user'
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API error response:', errorText);
      throw new Error(`API request failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ API response received');
    return data;
  } catch (error) {
    console.error('Error sending message:', error);
    throw new Error(`Failed to send analysis request: ${error.message}`);
  }
}

// Download all reports
async function downloadAllReports() {
  if (patentFamilyCheck.checked) {
    // Combine reports into one
    await downloadCombinedReport();
  } else {
    // Download each report separately
    for (const patent of patentQueue) {
      if (patent.status === 'completed' && patent.result) {
        await downloadReportForPatent(patent);
      }
    }
  }
}

// Download combined report for patent family
async function downloadCombinedReport() {
  const completedPatents = patentQueue.filter(p => p.status === 'completed' && p.result);

  if (completedPatents.length === 0) {
    showAlert('No completed patent analyses to download', 'warning');
    return;
  }

  // Try to download Excel report first
  try {
    showAlert('Attempting to download Excel report...', 'info');

    const response = await fetch('http://localhost:8080/download-report', {
      method: 'GET',
    });

    if (response.ok) {
      // Excel download successful
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);

      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = `patent-family-analysis-${Date.now()}.xlsx`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();

      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      showAlert(`Excel report downloaded: ${filename}`, 'success');
      return;
    }
  } catch (error) {
    console.log('Excel download failed, falling back to markdown:', error);
  }

  // Fallback to markdown download
  showAlert('Excel not available, generating markdown report...', 'warning');

  // Create a comprehensive report
  let reportContent = `# Patent Family Infringement Analysis Report\n\n`;
  reportContent += `## Patents in this family:\n`;
  completedPatents.forEach(patent => {
    reportContent += `- ${patent.patentNumber}\n`;
  });
  reportContent += `\n### Generated on: ${new Date().toLocaleString()}\n\n---\n\n`;

  // Add each patent's analysis with enhanced formatting
  completedPatents.forEach(patent => {
    reportContent += `# Analysis for ${patent.patentNumber}\n\n`;

    // Process the patent's result to extract content
    const patentContent = extractContentFromResult(patent.result, patent.patentNumber);

    reportContent += `## Summary\n${patentContent.summary || 'No summary available'}\n\n`;

    // Format products as table
    const formattedProducts = formatPotentiallyInfringingProducts(patentContent.products || 'No products identified');
    reportContent += `## Potentially Infringing Products\n${formattedProducts}\n\n`;

    // Format claim charts as table
    const formattedClaimChart = formatClaimChart(patentContent.claimChart || 'No claim chart analysis available');
    reportContent += `## Claim Chart Analysis\n${formattedClaimChart}\n\n`;
    reportContent += `---\n\n`;
  });

  reportContent += `*Report generated by Patent Infringement Analyzer*\n`;
  reportContent += `*Timestamp: ${new Date().toISOString()}*\n`;

  // Convert to blob and download
  const blob = new Blob([reportContent], { type: 'text/markdown;charset=utf-8' });
  const url = URL.createObjectURL(blob);

  // Create download link
  const a = document.createElement('a');
  a.href = url;
  a.download = `patent-family-analysis-${Date.now()}.md`;
  document.body.appendChild(a);
  a.click();

  // Clean up
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  showAlert(`Markdown report downloaded for ${completedPatents.length} patents`, 'success');
}

// Download report for a single patent
async function downloadReportForPatent(patent) {
  // Try to download Excel report first
  try {
    showAlert(`Attempting to download Excel report for ${patent.patentNumber}...`, 'info');

    const response = await fetch('http://localhost:8080/download-report', {
      method: 'GET',
    });

    if (response.ok) {
      // Excel download successful
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);

      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = `patent-analysis-${patent.patentNumber.replace(/[^\w\d]/g, '_')}-${Date.now()}.xlsx`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();

      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      showAlert(`Excel report downloaded: ${filename}`, 'success');
      return;
    }
  } catch (error) {
    console.log('Excel download failed, falling back to markdown:', error);
  }

  // Fallback to markdown download
  showAlert(`Excel not available for ${patent.patentNumber}, generating markdown report...`, 'warning');

  // Extract content from result
  const content = extractContentFromResult(patent.result, patent.patentNumber);

  // Create report content with enhanced formatting
  const reportContent = `# Patent Infringement Analysis Report

## Patent: ${patent.patentNumber}
### Generated on: ${new Date().toLocaleString()}

---

## Summary
${content.summary || 'No summary available'}

---

## Potentially Infringing Products
${formatPotentiallyInfringingProducts(content.products || 'No products identified')}

---

## Claim Chart Analysis
${formatClaimChart(content.claimChart || 'No claim chart analysis available')}

---

*Report generated by Patent Infringement Analyzer*
*Timestamp: ${new Date().toISOString()}*
`;

  // Convert to blob and download
  const blob = new Blob([reportContent], { type: 'text/markdown;charset=utf-8' });
  const url = URL.createObjectURL(blob);

  // Create download link
  const a = document.createElement('a');
  a.href = url;
  a.download = `patent-analysis-${patent.patentNumber.replace(/[^\w\d]/g, '_')}-${Date.now()}.md`;
  document.body.appendChild(a);
  a.click();

  // Clean up
  document.body.removeChild(a);
  URL.revokeObjectURL(url);

  showAlert(`Markdown report downloaded for ${patent.patentNumber}`, 'success');
}

// Debug database function
async function debugDatabase() {
  try {
    showAlert('Checking database status...', 'info');

    const response = await fetch('http://localhost:8080/api/debug-db', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({})
    });

    if (response.ok) {
      const data = await response.json();
      console.log('Database debug info:', data);

      let message = `Database Debug Info:\n\n`;
      message += `Path: ${data.database_path}\n`;
      message += `Tables: ${data.tables.join(', ')}\n`;
      message += `Patent Analysis Records: ${data.patent_analysis_count}\n`;
      message += `Patent Novelty Records: ${data.patent_novelty_count}\n`;
      message += `Infringed Models Records: ${data.infringed_models_count}\n`;

      if (data.sample_patents && data.sample_patents.length > 0) {
        message += `\nSample Patents: ${data.sample_patents.join(', ')}`;
      }

      alert(message);
      showAlert('Database debug completed', 'success');
    } else {
      showAlert('Failed to get database debug info', 'danger');
    }
  } catch (error) {
    console.error('Database debug error:', error);
    showAlert(`Database debug failed: ${error.message}`, 'danger');
  }
}

// Update multi-patent results tables
function updateMultiPatentResults() {
  const completedPatents = patentQueue.filter(p => p.status === 'completed' && p.result);

  if (completedPatents.length === 0) {
    return;
  }

  // Show the multi-patent results section
  multiPatentResults.classList.remove('d-none');

  // Combine all products from all patents
  const allProducts = [];
  const allClaimCharts = [];

  completedPatents.forEach(patent => {
    const content = extractContentFromResult(patent.result, patent.patentNumber);

    // Extract products
    if (content.products) {
      const productLines = content.products.split('\n').filter(line => line.trim() !== '');
      let currentProduct = { patent: patent.patentNumber };

      productLines.forEach(line => {
        const trimmedLine = line.trim();
        if (trimmedLine.toLowerCase().includes('company:')) {
          if (Object.keys(currentProduct).length > 1) {
            allProducts.push(currentProduct);
          }
          currentProduct = {
            patent: patent.patentNumber,
            company: trimmedLine.split(':')[1]?.trim() || ''
          };
        } else if (trimmedLine.toLowerCase().includes('model:')) {
          currentProduct.model = trimmedLine.split(':')[1]?.trim() || '';
        } else if (trimmedLine.toLowerCase().includes('launch date:')) {
          currentProduct.date = trimmedLine.split(':')[1]?.trim() || '';
        } else if (trimmedLine.toLowerCase().includes('risk:')) {
          currentProduct.risk = trimmedLine.split(':')[1]?.trim() || '';
        } else if (trimmedLine.toLowerCase().includes('category:')) {
          currentProduct.category = trimmedLine.split(':')[1]?.trim() || '';
        }
      });

      if (Object.keys(currentProduct).length > 1) {
        allProducts.push(currentProduct);
      }
    }

    // Extract claim charts
    if (content.claimChart) {
      const claimLines = content.claimChart.split('\n').filter(line => line.trim() !== '');
      claimLines.forEach(line => {
        if (line.includes('|') && !line.includes('---')) {
          allClaimCharts.push({
            patent: patent.patentNumber,
            content: line
          });
        }
      });
    }
  });

  // Create combined products table
  if (allProducts.length > 0) {
    let productsHTML = '<table class="table table-striped products-table">';
    productsHTML += '<thead><tr>';
    productsHTML += '<th>Patent</th><th>Company</th><th>Model</th><th>Category</th><th>Launch Date</th><th>Risk Assessment</th>';
    productsHTML += '</tr></thead><tbody>';

    allProducts.forEach(product => {
      productsHTML += '<tr>';
      productsHTML += `<td><strong>${product.patent}</strong></td>`;
      productsHTML += `<td>${product.company || 'Not specified'}</td>`;
      productsHTML += `<td>${product.model || 'Not specified'}</td>`;
      productsHTML += `<td>${product.category || 'Not specified'}</td>`;
      productsHTML += `<td>${product.date || 'Not specified'}</td>`;

      let riskClass = '';
      const risk = (product.risk || '').toLowerCase();
      if (risk.includes('high')) riskClass = 'risk-high';
      else if (risk.includes('medium')) riskClass = 'risk-medium';
      else if (risk.includes('low')) riskClass = 'risk-low';

      productsHTML += `<td class="${riskClass}">${product.risk || 'Not assessed'}</td>`;
      productsHTML += '</tr>';
    });

    productsHTML += '</tbody></table>';
    combinedProductsContent.innerHTML = productsHTML;
  } else {
    combinedProductsContent.innerHTML = '<p class="text-muted">No products found across analyzed patents.</p>';
  }

  // Create combined claim charts summary
  if (allClaimCharts.length > 0) {
    let claimChartsHTML = '<table class="table table-striped claim-chart-table">';
    claimChartsHTML += '<thead><tr>';
    claimChartsHTML += '<th>Patent</th><th>Claim Analysis Summary</th>';
    claimChartsHTML += '</tr></thead><tbody>';

    const patentSummaries = {};
    allClaimCharts.forEach(chart => {
      if (!patentSummaries[chart.patent]) {
        patentSummaries[chart.patent] = [];
      }
      patentSummaries[chart.patent].push(chart.content);
    });

    Object.keys(patentSummaries).forEach(patent => {
      claimChartsHTML += '<tr>';
      claimChartsHTML += `<td><strong>${patent}</strong></td>`;
      claimChartsHTML += `<td>${patentSummaries[patent].length} claim chart entries analyzed</td>`;
      claimChartsHTML += '</tr>';
    });

    claimChartsHTML += '</tbody></table>';
    combinedClaimChartsContent.innerHTML = claimChartsHTML;
  } else {
    combinedClaimChartsContent.innerHTML = '<p class="text-muted">No claim charts found across analyzed patents.</p>';
  }
}

// Extract content from API result
function extractContentFromResult(result, patentNumber) {
  let summaryText = '';
  let productsText = '';
  let claimChartText = '';

  // Check if response is an array and process each item
  if (Array.isArray(result)) {
    // Process each item in the response
    result.forEach(item => {
      // Process content parts
      if (item.content && item.content.parts) {
        item.content.parts.forEach(part => {
          if (part.text) {
            const text = part.text;

            if (text.toLowerCase().includes('product') ||
                text.toLowerCase().includes('company') ||
                text.toLowerCase().includes('competitor') ||
                text.toLowerCase().includes('infringing')) {
              productsText += text + '\n\n';
            } else if (text.toLowerCase().includes('claim chart') ||
                      text.toLowerCase().includes('element') ||
                      text.toLowerCase().includes('limitation') ||
                      text.toLowerCase().includes('claim element')) {
              claimChartText += text + '\n\n';
            } else {
              summaryText += text + '\n\n';
            }
          }
        });
      }

      // Process structured data from state_delta
      if (item.actions && item.actions.state_delta) {
        const stateDelta = item.actions.state_delta;

        if (stateDelta.infringement_agent_output) {
          summaryText += "## Analysis Output\n\n" + stateDelta.infringement_agent_output + "\n\n";
        }

        if (stateDelta.product_search || stateDelta.competition_search_output) {
          const productData = stateDelta.product_search || stateDelta.competition_search_output;
          productsText += formatProductData(productData);
        }

        if (stateDelta.claim_chart_output) {
          claimChartText += "## Claim Chart Analysis\n\n" + formatClaimChart(stateDelta.claim_chart_output) + "\n\n";
        }
      }
    });
  }

  return {
    summary: summaryText,
    products: productsText,
    claimChart: claimChartText
  };
}

// Improved function to format claim charts consistently with deduplication
function formatClaimChart(text) {
  if (!text || typeof text !== 'string') return '';

  // Remove duplicate sections first
  text = removeDuplicateSections(text);

  // Filter out unwanted sections
  text = filterUnwantedSections(text);

  // Check if it's already a properly formatted table
  if (text.includes('|') && text.includes('---')) {
    return enhanceTableStyling(text, 'claim-chart-table');
  }

  // Try to parse structured claim chart data
  const lines = text.trim().split('\n').filter(line => line.trim() !== '');
  if (lines.length < 2) return text;

  // Look for patterns that indicate claim chart structure
  let formattedTable = '';
  let currentTable = [];
  let isInTable = false;
  const seenProducts = new Set(); // Track products to avoid duplicates

  for (const line of lines) {
    const trimmedLine = line.trim();
    
    // Skip empty lines
    if (!trimmedLine) continue;

    // Detect table headers based on common claim chart patterns
    if (trimmedLine.toLowerCase().includes('claim element') || 
        trimmedLine.toLowerCase().includes('corresponding feature') ||
        trimmedLine.toLowerCase().includes('product feature') ||
        trimmedLine.toLowerCase().includes('source justification')) {
      
      if (!isInTable) {
        // Start new table
        const headers = trimmedLine.split(/\s*\|\s*/).filter(h => h.trim());
        if (headers.length > 1) {
          formattedTable += `\n| ${headers.join(' | ')} |\n`;
          formattedTable += `| ${headers.map(() => '---').join(' | ')} |\n`;
          isInTable = true;
          continue;
        }
      }
    }

    // Process table rows
    if (isInTable) {
      const cells = trimmedLine.split(/\s*\|\s*/).filter(c => c.trim());
      if (cells.length > 1) {
        formattedTable += `| ${cells.join(' | ')} |\n`;
      } else if (trimmedLine.includes('---') || trimmedLine === '') {
        // End of table
        isInTable = false;
        formattedTable += '\n';
      } else {
        // Single line content, might be continuation
        formattedTable += `| ${trimmedLine} | | |\n`;
      }
    } else {
      // Check for product headers to avoid duplicates
      if (trimmedLine.toLowerCase().startsWith('product:')) {
        const productName = trimmedLine.toLowerCase();
        if (seenProducts.has(productName)) {
          // Skip this product section as it's a duplicate
          continue;
        }
        seenProducts.add(productName);
      }

      // Regular text outside tables
      formattedTable += trimmedLine + '\n';
    }
  }

  return formattedTable || text;
}

// Enhanced table styling function
function enhanceTableStyling(markdownTable, tableClass = '') {
  if (!markdownTable || !markdownTable.includes('|')) return markdownTable;

  const lines = markdownTable.trim().split('\n');
  const tableLines = lines.filter(line => line.includes('|'));

  if (tableLines.length < 2) return markdownTable;

  // Parse header
  const headerCells = tableLines[0].split('|').map(cell => cell.trim()).filter(cell => cell);

  // Skip separator line and parse data rows
  const dataRows = tableLines.slice(2).map(line =>
    line.split('|').map(cell => cell.trim()).filter(cell => cell)
  );

  // Build HTML table
  let html = `<table class="table table-striped ${tableClass}">`;

  // Add header
  html += '<thead><tr>';
  headerCells.forEach(header => {
    html += `<th>${header}</th>`;
  });
  html += '</tr></thead>';

  // Add body
  html += '<tbody>';
  dataRows.forEach(row => {
    html += '<tr>';
    row.forEach((cell, index) => {
      let cellClass = '';

      // Add special styling for risk assessment columns
      if (headerCells[index] && headerCells[index].toLowerCase().includes('risk')) {
        if (cell.toLowerCase().includes('high')) cellClass = 'risk-high';
        else if (cell.toLowerCase().includes('medium')) cellClass = 'risk-medium';
        else if (cell.toLowerCase().includes('low')) cellClass = 'risk-low';
      }

      // Add column-specific classes for claim charts
      if (tableClass.includes('claim-chart')) {
        if (index === 0) cellClass += ' claim-element-col';
        else if (index === 1) cellClass += ' feature-col';
        else if (index === 2) cellClass += ' justification-col';
      }

      html += `<td class="${cellClass}">${cell}</td>`;
    });
    html += '</tr>';
  });
  html += '</tbody></table>';

  return html;
}

// Format potentially infringing products consistently
function formatPotentiallyInfringingProducts(text) {
  if (!text || typeof text !== 'string') return '';

  // Check if it's already a table
  if (text.includes('|') && text.includes('---')) {
    return enhanceTableStyling(text, 'products-table');
  }

  // Try to extract product information and create a table
  const lines = text.trim().split('\n').filter(line => line.trim() !== '');
  const products = [];

  // Look for structured product patterns in the text
  let currentProduct = {};
  let inProductSection = false;

  lines.forEach(line => {
    const trimmedLine = line.trim();

    // Detect start of product information
    if (trimmedLine.toLowerCase().includes('company:') ||
        trimmedLine.toLowerCase().includes('manufacturer:') ||
        trimmedLine.match(/^Company\s*[:]\s*/i)) {

      // Save previous product if exists
      if (Object.keys(currentProduct).length > 0) {
        products.push(currentProduct);
      }

      // Start new product
      currentProduct = {
        company: trimmedLine.split(':')[1]?.trim() || trimmedLine.replace(/^Company\s*[:]\s*/i, '').trim()
      };
      inProductSection = true;

    } else if (inProductSection && (trimmedLine.toLowerCase().includes('model:') ||
                                   trimmedLine.toLowerCase().includes('product:'))) {
      currentProduct.model = trimmedLine.split(':')[1]?.trim() || '';

    } else if (inProductSection && (trimmedLine.toLowerCase().includes('launch date:') ||
                                    trimmedLine.toLowerCase().includes('date:'))) {
      currentProduct.date = trimmedLine.split(':')[1]?.trim() || '';

    } else if (inProductSection && (trimmedLine.toLowerCase().includes('risk:') ||
                                    trimmedLine.toLowerCase().includes('assessment:'))) {
      currentProduct.risk = trimmedLine.split(':')[1]?.trim() || '';

    } else if (inProductSection && (trimmedLine.toLowerCase().includes('category:') ||
                                    trimmedLine.toLowerCase().includes('type:'))) {
      currentProduct.category = trimmedLine.split(':')[1]?.trim() || '';

    } else if (inProductSection && (trimmedLine.toLowerCase().includes('evidence:') ||
                                    trimmedLine.toLowerCase().includes('justification:'))) {
      currentProduct.justification = trimmedLine.split(':')[1]?.trim() || '';

    } else if (trimmedLine === '' || trimmedLine.startsWith('#')) {
      // End of current product section
      if (Object.keys(currentProduct).length > 0) {
        products.push(currentProduct);
        currentProduct = {};
      }
      inProductSection = false;
    }
  });

  // Add the last product
  if (Object.keys(currentProduct).length > 0) {
    products.push(currentProduct);
  }

  // If we found products, create a table
  if (products.length > 0) {
    let html = '<table class="table table-striped products-table">';
    html += '<thead><tr>';
    html += '<th>Company</th><th>Model</th><th>Category</th><th>Launch Date</th><th>Risk Assessment</th><th>Risk Justification</th>';
    html += '</tr></thead><tbody>';

    products.forEach(product => {
      html += '<tr>';
      html += `<td>${product.company || 'Not specified'}</td>`;
      html += `<td>${product.model || 'Not specified'}</td>`;
      html += `<td>${product.category || 'Not specified'}</td>`;
      html += `<td>${product.date || 'Not specified'}</td>`;

      let riskClass = '';
      const risk = (product.risk || '').toLowerCase();
      if (risk.includes('high')) riskClass = 'risk-high';
      else if (risk.includes('medium')) riskClass = 'risk-medium';
      else if (risk.includes('low')) riskClass = 'risk-low';

      html += `<td class="${riskClass}">${product.risk || 'Not assessed'}</td>`;
      html += `<td>${product.justification || 'Not provided'}</td>`;
      html += '</tr>';
    });

    html += '</tbody></table>';
    return html;
  }

  return text;
}

// Enhanced content extraction and formatting with deduplication
function extractAndFormatContent(response, patentNumber) {
  const sections = {
    summary: `# Patent Analysis Summary: ${patentNumber}\n\n`,
    novelty: `# Novelty Analysis: ${patentNumber}\n\n`,
    products: `# Potentially Infringing Products: ${patentNumber}\n\n`,
    claimChart: `# Claim Chart Analysis: ${patentNumber}\n\n`
  };

  let hasError = false;
  let errorMessage = '';
  const processedContent = new Set(); // Track processed content to avoid duplicates

  if (Array.isArray(response)) {
    for (const item of response) {
      // Process content parts
      if (item.content && item.content.parts) {
        item.content.parts.forEach(part => {
          if (part.text) {
            let text = part.text.trim();

            // Skip if we've already processed this exact content
            if (processedContent.has(text)) {
              return;
            }
            processedContent.add(text);

            // Filter out unwanted sections
            if (text.toLowerCase().includes('analysis status') ||
                text.toLowerCase().includes('patent number:') ||
                text.toLowerCase().includes('analysis date:') ||
                text.toLowerCase().includes('✅ complete') ||
                text.toLowerCase().includes('⏳ pending') ||
                text.toLowerCase().includes('claim chart: ⏳ pending')) {
              return; // Skip this content
            }

            // Check for errors
            if (text.toLowerCase().includes('unable to retrieve') ||
                text.toLowerCase().includes('error') ||
                text.toLowerCase().includes('not found')) {
              hasError = true;
              errorMessage = text;
              return;
            }

            // Clean up the text - remove duplicate sections
            text = removeDuplicateSections(text);

            // Add to summary (but filter out repetitive content)
            if (!isDuplicateContent(sections.summary, text)) {
              sections.summary += text + '\n\n';
            }

            // Categorize content for specific tabs
            const lowerText = text.toLowerCase();

            if (lowerText.includes('novelty') || lowerText.includes('prior art') ||
                lowerText.includes('patentability') || lowerText.includes('anticipat')) {
              if (!isDuplicateContent(sections.novelty, text)) {
                sections.novelty += text + '\n\n';
              }
            }

            if (lowerText.includes('potentially infringing') || lowerText.includes('competitor') ||
                lowerText.includes('product') || lowerText.includes('company') ||
                lowerText.includes('manufacturer')) {
              if (!isDuplicateContent(sections.products, text)) {
                sections.products += text + '\n\n';
              }
            }

            if (lowerText.includes('claim chart') || lowerText.includes('claim element') ||
                lowerText.includes('corresponding feature') || lowerText.includes('infringement analysis')) {
              if (!isDuplicateContent(sections.claimChart, text)) {
                sections.claimChart += text + '\n\n';
              }
            }
          }
        });
      }

      // Process structured data from state_delta
      if (item.actions && item.actions.state_delta) {
        const stateDelta = item.actions.state_delta;

        if (stateDelta.infringement_agent_output) {
          const output = stateDelta.infringement_agent_output;

          // Filter out unwanted sections from structured output
          const cleanOutput = filterUnwantedSections(output);

          if (cleanOutput && !isDuplicateContent(sections.summary, cleanOutput)) {
            sections.summary += "## Analysis Output\n\n" + cleanOutput + "\n\n";

            const lowerOutput = cleanOutput.toLowerCase();
            if (lowerOutput.includes('novelty') || lowerOutput.includes('prior art')) {
              sections.novelty += "## Novelty Analysis\n\n" + cleanOutput + "\n\n";
            }
          }
        }

        if (stateDelta.product_search || stateDelta.competition_search_output) {
          const productData = stateDelta.product_search || stateDelta.competition_search_output;
          const formattedProducts = formatProductData(productData);

          if (!isDuplicateContent(sections.summary, formattedProducts)) {
            sections.summary += formattedProducts;
            sections.products += formattedProducts;
          }
        }

        if (stateDelta.claim_chart_output) {
          const formattedChart = formatClaimChart(stateDelta.claim_chart_output);

          if (!isDuplicateContent(sections.summary, formattedChart)) {
            sections.summary += "## Detailed Claim Charts\n\n" + formattedChart + "\n\n";
            sections.claimChart += "## Detailed Claim Charts\n\n" + formattedChart + "\n\n";
          }
        }
      }
    }
  }

  // Handle error cases
  if (hasError) {
    const errorSection = `## ⚠️ Analysis Error\n\n${errorMessage}\n\n`;
    const errorNote = `Please ensure the patent number "${patentNumber}" is correct and try again.\n\n`;

    Object.keys(sections).forEach(key => {
      sections[key] += errorSection + errorNote;
    });
  }

  // Add metadata to all sections
  const metadata = `---\n\n*Analysis completed on: ${new Date().toLocaleString()}*\n*Patent Number: ${patentNumber}*\n*Session ID: ${currentSessionId}*\n`;
  Object.keys(sections).forEach(key => {
    sections[key] += metadata;
  });

  return sections;
}

// Helper function to check for duplicate content
function isDuplicateContent(existingContent, newContent) {
  if (!existingContent || !newContent) return false;

  // Normalize content for comparison
  const normalizeText = (text) => text.toLowerCase().replace(/\s+/g, ' ').trim();
  const normalizedExisting = normalizeText(existingContent);
  const normalizedNew = normalizeText(newContent);

  // Check if the new content is already contained in existing content
  return normalizedExisting.includes(normalizedNew) || normalizedNew.length < 50;
}

// Helper function to remove duplicate sections within text
function removeDuplicateSections(text) {
  if (!text) return text;

  // Split by common section headers and remove duplicates
  const lines = text.split('\n');
  const uniqueLines = [];
  const seenLines = new Set();

  for (const line of lines) {
    const normalizedLine = line.toLowerCase().trim();

    // Skip empty lines and very short lines
    if (normalizedLine.length < 10) {
      uniqueLines.push(line);
      continue;
    }

    // Check for duplicate lines
    if (!seenLines.has(normalizedLine)) {
      seenLines.add(normalizedLine);
      uniqueLines.push(line);
    }
  }

  return uniqueLines.join('\n');
}

// Helper function to filter out unwanted sections
function filterUnwantedSections(text) {
  if (!text) return text;

  const lines = text.split('\n');
  const filteredLines = [];
  let skipSection = false;

  for (const line of lines) {
    const lowerLine = line.toLowerCase().trim();

    // Check if we should skip this section
    if (lowerLine.includes('analysis status') ||
        lowerLine.includes('patent number:') ||
        lowerLine.includes('analysis date:') ||
        lowerLine.includes('✅ complete') ||
        lowerLine.includes('⏳ pending') ||
        lowerLine.includes('claim chart: ⏳ pending')) {
      skipSection = true;
      continue;
    }

    // Check if we're starting a new section (reset skip flag)
    if (lowerLine.startsWith('#') || lowerLine.startsWith('##') || lowerLine.startsWith('###')) {
      skipSection = false;
    }

    // Add line if we're not skipping
    if (!skipSection) {
      filteredLines.push(line);
    }
  }

  return filteredLines.join('\n').trim();
}

// Improved function to format product data consistently
function formatProductData(productData) {
  let formatted = "## Potentially Infringing Products\n\n";

  try {
    // Try to parse as JSON if it's a string
    const products = typeof productData === 'string' ? JSON.parse(productData) : productData;

    if (Array.isArray(products)) {
      if (products.length === 0) {
        formatted += "No potential infringing products found.\n\n";
      } else {
        // Create consistent table format
        formatted += "| Company | Model | Launch Date | Risk Level |\n";
        formatted += "| --- | --- | --- | --- |\n";

        products.forEach(product => {
          const company = product.company || 'Unknown Company';
          const model = product.model || 'Unknown Model';
          const launchDate = product.launch_date || 'Not specified';
          const riskLevel = product.infringement_risk || 'Not assessed';
          
          formatted += `| ${company} | ${model} | ${launchDate} | ${riskLevel} |\n`;
        });

        formatted += "\n### Detailed Product Information\n\n";
        
        products.forEach((product, index) => {
          formatted += `#### ${index + 1}. ${product.company || 'Unknown Company'} - ${product.model || 'Unknown Model'}\n\n`;
          
          if (product.launch_date) formatted += `**Launch Date:** ${product.launch_date}\n\n`;
          if (product.competitor_type) formatted += `**Competitor Type:** ${product.competitor_type}\n\n`;
          if (product.infringement_risk) formatted += `**Infringement Risk:** ${product.infringement_risk}\n\n`;

          if (product.infringement_evidence_links && product.infringement_evidence_links.length > 0) {
            formatted += `**Evidence Links:**\n`;
            product.infringement_evidence_links.forEach(link => {
              formatted += `- [${link}](${link})\n`;
            });
            formatted += "\n";
          }
          formatted += "---\n\n";
        });
      }
    } else if (typeof products === 'object') {
      formatted += "```json\n" + JSON.stringify(products, null, 2) + "\n```\n\n";
    } else {
      formatted += productData.toString() + "\n\n";
    }
  } catch (e) {
    // If parsing fails, just add as text
    formatted += productData.toString() + "\n\n";
  }

  return formatted;
}

// Global variables for current analysis
let currentPatentNumber = null;
let pendingAnalysis = false;

// Show confirmation dialog
function showConfirmationDialog(patentNumber, message) {
  currentPatentNumber = patentNumber;
  confirmationMessage.innerHTML = message;
  confirmationBackdrop.classList.remove('d-none');
  confirmationDialog.classList.remove('d-none');
}

// Hide confirmation dialog
function hideConfirmationDialog() {
  confirmationBackdrop.classList.add('d-none');
  confirmationDialog.classList.add('d-none');
  currentPatentNumber = null;
}

// Proceed with new analysis
async function proceedWithNewAnalysis() {
  if (!currentPatentNumber) return;

  try {
    showAlert('Starting new analysis...', 'info');
    await performNewAnalysis(currentPatentNumber);
  } catch (error) {
    console.error('Error in new analysis:', error);
    showAlert(`Analysis failed: ${error.message}`, 'danger');
  }
}

// Use existing analysis
async function useExistingAnalysis() {
  if (!currentPatentNumber) return;

  try {
    // Hide confirmation dialog first
    hideConfirmationDialog();

    // Show loading indicator with descriptive text
    showLoadingIndicator('Retrieving data from database...');
    resultsContainer.classList.add('d-none');

    showAlert('Loading existing analysis...', 'info');
    const patentData = await loadPatentFromDatabase(currentPatentNumber);
    if (patentData && patentData.has_data) {
      await displayExistingAnalysis(patentData, currentPatentNumber);
    } else {
      showAlert('No meaningful existing analysis found, starting new analysis...', 'warning');
      await performNewAnalysis(currentPatentNumber);
    }
  } catch (error) {
    console.error('Error loading existing analysis:', error);
    showAlert(`Failed to load existing analysis: ${error.message}`, 'danger');
  } finally {
    hideLoadingIndicator();
  }
}

// Main analysis starter function
async function startAnalysis() {
  const patentNumber = patentInput.value.trim();

  if (!patentNumber) {
    showAlert('Please enter a patent number', 'danger');
    return;
  }

  if (!validatePatentNumber(patentNumber)) {
    showAlert('Please enter a valid patent number format (e.g., US10741843B2)', 'warning');
    return;
  }

  // Check if patent exists in database first
  try {
    const existingData = await checkPatentInDatabase(patentNumber);
    console.log('Database check result:', existingData);

    if (existingData && existingData.exists) {
      // Show confirmation dialog
      const message = `
        <p>An analysis for patent <strong>${patentNumber}</strong> already exists. The previous analysis did not identify any potentially infringing products.</p>
        <p>Would you like to proceed with a new, comprehensive analysis? (Yes/No)</p>
      `;
      showConfirmationDialog(patentNumber, message);
      return;
    }
  } catch (error) {
    console.log('Database check failed, proceeding with new analysis:', error);
  }

  // If no existing data, proceed with new analysis
  console.log(`No existing data found for ${patentNumber}, starting new analysis...`);
  await performNewAnalysis(patentNumber);
}

// Validate patent number format
function validatePatentNumber(patentNumber) {
  const patterns = [
    /^US\d{7,8}[A-Z]\d?$/i,  // US patents: US1234567B2, US12345678B1
    /^EP\d{7}[A-Z]\d?$/i,    // European patents: EP1234567A1
    /^WO\d{4}\/\d{6}$/i,     // WIPO patents: WO2023/123456
    /^\d{7,8}$/,             // Patent numbers without country code
  ];

  return patterns.some(pattern => pattern.test(patentNumber));
}

// Check if patent exists in database
async function checkPatentInDatabase(patentNumber) {
  try {
    const response = await fetch('http://localhost:8080/api/check-patent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ patent_number: patentNumber })
    });

    if (response.ok) {
      const data = await response.json();
      return data.exists ? data : null;
    }
    return null;
  } catch (error) {
    console.log('Database check failed:', error);
    return null;
  }
}

// Load patent data from database
async function loadPatentFromDatabase(patentNumber) {
  try {
    const response = await fetch('http://localhost:8080/api/get-patent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ patent_number: patentNumber })
    });

    if (response.ok) {
      const data = await response.json();
      return data;
    }
    return null;
  } catch (error) {
    console.error('Error loading patent from database:', error);
    return null;
  }
}

// Perform new analysis
async function performNewAnalysis(patentNumber) {
  try {
    // Show loading indicator
    loadingIndicator.classList.remove('d-none');
    resultsContainer.classList.add('d-none');

    // Create a new session
    const sessionId = 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    await createSessionForPatent(sessionId);

    // Get custom prompt if any
    const customInstructions = customPrompt.value.trim();
    let message = `Please analyze patent ${patentNumber} for potential infringement.

CRITICAL REQUIREMENTS:
1. Generate Excel claim charts report in generated_reports directory using the claim_chart_to_excel tool
2. Save all analysis data to the database using the appropriate database tools (save_patent_analysis, save_patent_novelty, save_claim_charts)
3. DO NOT include any "Analysis Status" section in your output
4. DO NOT repeat content or sections - provide each piece of information only once
5. DO NOT include status indicators like "✅ Complete" or "⏳ Pending"
6. Ensure proper table formatting for all outputs

Please structure your response with the following sections (ONLY ONCE EACH):

1. **Executive Summary**: Brief overview of findings
2. **Potentially Infringing Products**: Present as a well-formatted table with columns: Company, Model, Category, Launch Date, Risk Assessment, Risk Justification
3. **Detailed Claim Charts**: Present as structured tables with columns: Claim Element, Corresponding Feature in Product, Source Justification
4. **Novelty Analysis**: Include prior art search and novelty assessment

Use consistent table formatting with proper markdown tables. Ensure all risk assessments are clearly categorized as High, Medium, or Low. Do deep research and provide comprehensive analysis.

CRITICAL:
- Use the claim_chart_to_excel tool to generate the Excel report file in the generated_reports directory
- Save all analysis data to the database for future retrieval using the database tools
- Provide content only once - no repetition of sections or information
- Keep output concise and well-organized`;

    if (customInstructions) {
      message += `. ${customInstructions}`;
    }

    // Send the analysis request
    const response = await sendMessageForPatent(sessionId, message);

    // Process and display the response
    await displayAnalysisResults(response, patentNumber);

    showAlert(`Analysis completed for patent ${patentNumber}`, 'success');

  } catch (error) {
    console.error('Error in new analysis:', error);
    showAlert(`Analysis failed: ${error.message}`, 'danger');
  } finally {
    loadingIndicator.classList.add('d-none');
  }
}

// Display existing analysis from database
async function displayExistingAnalysis(patentData, patentNumber) {
  try {
    // Show loading indicator briefly
    loadingIndicator.classList.remove('d-none');

    console.log('Displaying existing analysis for:', patentNumber);
    console.log('Patent data received:', patentData);

    // Check if we actually have meaningful data
    if (!patentData || !patentData.has_data) {
      console.log('No meaningful data found, proceeding with new analysis');
      await performNewAnalysis(patentNumber);
      return;
    }

    // Check if we have the reconstructed analysis_data
    if (patentData.analysis_data && typeof patentData.analysis_data === 'string' && patentData.analysis_data.trim().length > 0) {
      // The analysis_data is already reconstructed as a formatted string
      summaryContent.innerHTML = marked.parse(patentData.analysis_data);
    } else {
      // Fallback: manually reconstruct the display
      const sections = await processExistingPatentDataLegacy(patentData, patentNumber);
      if (sections && sections.summary && sections.summary.trim().length > 0) {
        summaryContent.innerHTML = marked.parse(sections.summary);
      } else {
        console.log('No meaningful content to display, proceeding with new analysis');
        await performNewAnalysis(patentNumber);
        return;
      }
    }

    // Show results
    resultsContainer.classList.remove('d-none');
    showAlert(`Existing analysis loaded for patent ${patentNumber}`, 'success');

  } catch (error) {
    console.error('Error displaying existing analysis:', error);
    showAlert(`Failed to display existing analysis: ${error.message}`, 'danger');
  } finally {
    loadingIndicator.classList.add('d-none');
  }
}

// Display analysis results (for new analysis)
async function displayAnalysisResults(response, patentNumber) {
  try {
    const sections = extractAndFormatContent(response, patentNumber);

    // Display the summary content
    summaryContent.innerHTML = marked.parse(sections.summary);

    // Show results container
    resultsContainer.classList.remove('d-none');

  } catch (error) {
    console.error('Error displaying analysis results:', error);
    showAlert(`Failed to display analysis results: ${error.message}`, 'danger');
  }
}

// Legacy function for processing existing patent data (fallback)
async function processExistingPatentDataLegacy(patentData, patentNumber) {
  console.log('Processing existing patent data for:', patentNumber);
  console.log('Patent data received:', patentData);
  console.log('Analysis data:', patentData.analysis_data);
  console.log('Novelty data:', patentData.novelty_data);
  console.log('Novelty data type:', typeof patentData.novelty_data);
  console.log('Infringed models:', patentData.infringed_models);

  // Create sections from database data
  const sections = {
    summary: `# Patent Analysis Summary: ${patentNumber}\n\n`,
    novelty: `# Novelty Analysis: ${patentNumber}\n\n`,
    products: `# Potentially Infringing Products: ${patentNumber}\n\n`,
    claimChart: `# Claim Chart Analysis: ${patentNumber}\n\n`
  };

  // Process analysis data - handle both string and object formats
  if (patentData.analysis_data && patentData.analysis_data !== null) {
    let analysisText = '';
    if (typeof patentData.analysis_data === 'string') {
      analysisText = patentData.analysis_data;
    } else if (typeof patentData.analysis_data === 'object') {
      analysisText = JSON.stringify(patentData.analysis_data, null, 2);
    }
    sections.summary += `## Analysis Summary\n\n${analysisText}\n\n`;
  }

  // Process novelty data - handle both string and object formats
  if (patentData.novelty_data && patentData.novelty_data !== null) {
    let noveltyText = '';
    console.log('Processing novelty data:', patentData.novelty_data);

    if (typeof patentData.novelty_data === 'string') {
      noveltyText = patentData.novelty_data;
    } else if (typeof patentData.novelty_data === 'object' && patentData.novelty_data !== null) {
      // Extract meaningful text from novelty object
      if (patentData.novelty_data.novelty_summary) {
        noveltyText = patentData.novelty_data.novelty_summary;
      } else if (patentData.novelty_data.summary) {
        noveltyText = patentData.novelty_data.summary;
      } else if (patentData.novelty_data.analysis) {
        noveltyText = patentData.novelty_data.analysis;
      } else {
        // If no specific field, try to extract readable content
        const keys = Object.keys(patentData.novelty_data);
        console.log('Novelty data keys:', keys);
        if (keys.length > 0) {
          noveltyText = keys.map(key => {
            const value = patentData.novelty_data[key];
            if (typeof value === 'string') {
              return `**${key}**: ${value}`;
            } else if (typeof value === 'object' && value !== null) {
              return `**${key}**: ${JSON.stringify(value, null, 2)}`;
            } else {
              return `**${key}**: ${String(value)}`;
            }
          }).join('\n\n');
        } else {
          noveltyText = 'Novelty data available but format not recognized';
        }
      }
    }

    if (noveltyText) {
      sections.novelty += `## Patent Novelty\n\n${noveltyText}\n\n`;
      // Also add to summary if no analysis data
      if (!patentData.analysis_data) {
        sections.summary += `## Novelty Analysis\n\n${noveltyText}\n\n`;
      }
    }
  }

  // Process infringed models as products
  if (patentData.infringed_models && patentData.infringed_models.length > 0) {
    let productsHTML = '<table class="table table-striped products-table">';
    productsHTML += '<thead><tr>';
    productsHTML += '<th>Company</th><th>Model</th><th>Category</th><th>Launch Date</th><th>Risk Assessment</th><th>Risk Justification</th>';
    productsHTML += '</tr></thead><tbody>';

    patentData.infringed_models.forEach(model => {
      productsHTML += '<tr>';
      productsHTML += `<td>${model.company || 'Not specified'}</td>`;
      productsHTML += `<td>${model.model || model.product_name || 'Not specified'}</td>`;
      productsHTML += `<td>${model.category || model.product_category || 'Not specified'}</td>`;
      productsHTML += `<td>${model.launch_date || model.date || 'Not specified'}</td>`;

      let riskClass = '';
      const risk = (model.infringement_risk || model.risk || '').toLowerCase();
      if (risk.includes('high')) riskClass = 'risk-high';
      else if (risk.includes('medium')) riskClass = 'risk-medium';
      else if (risk.includes('low')) riskClass = 'risk-low';

      productsHTML += `<td class="${riskClass}">${model.infringement_risk || model.risk || 'Not assessed'}</td>`;
      productsHTML += `<td>${model.risk_justification || model.justification || 'Not provided'}</td>`;
      productsHTML += '</tr>';
    });

    productsHTML += '</tbody></table>';
    sections.products += productsHTML;

    // Process claim charts
    let claimChartsHTML = '<table class="table table-striped claim-chart-table">';
    claimChartsHTML += '<thead><tr>';
    claimChartsHTML += '<th>Product</th><th>Claim Element</th><th>Corresponding Feature</th><th>Source Justification</th>';
    claimChartsHTML += '</tr></thead><tbody>';

    let hasClaimCharts = false;

    patentData.infringed_models.forEach(model => {
      // Check for both claim_chart (singular) and claim_charts (plural) from database
      const claimChartsData = model.claim_charts || model.claim_chart;

      if (claimChartsData && claimChartsData.length > 0) {
        try {
          let claimCharts = claimChartsData;

          // Handle different formats of claim chart data
          if (typeof claimCharts === 'string') {
            try {
              claimCharts = JSON.parse(claimCharts);
            } catch (e) {
              // If JSON parsing fails, treat as plain text
              claimChartsHTML += '<tr>';
              claimChartsHTML += `<td>${model.model || model.product_name || 'Unknown'}</td>`;
              claimChartsHTML += `<td class="claim-element-col" colspan="3">${claimCharts}</td>`;
              claimChartsHTML += '</tr>';
              hasClaimCharts = true;
              return;
            }
          }

          // Ensure we have an array
          if (!Array.isArray(claimCharts)) {
            claimCharts = [claimCharts];
          }

          claimCharts.forEach(chart => {
            claimChartsHTML += '<tr>';
            claimChartsHTML += `<td>${model.model || model.product_name || 'Unknown'}</td>`;
            claimChartsHTML += `<td class="claim-element-col">${chart.claim_element || chart.element || ''}</td>`;
            claimChartsHTML += `<td class="feature-col">${chart.corresponding_feature || chart.feature || ''}</td>`;
            claimChartsHTML += `<td class="justification-col">${chart.source_justification || chart.justification || ''}</td>`;
            claimChartsHTML += '</tr>';
            hasClaimCharts = true;
          });
        } catch (e) {
          console.error('Error parsing claim chart:', e);
          // Add error row
          claimChartsHTML += '<tr>';
          claimChartsHTML += `<td>${model.model || model.product_name || 'Unknown'}</td>`;
          claimChartsHTML += `<td class="claim-element-col" colspan="3">Error parsing claim chart data</td>`;
          claimChartsHTML += '</tr>';
        }
      }
    });

    claimChartsHTML += '</tbody></table>';

    if (hasClaimCharts) {
      sections.claimChart += claimChartsHTML;
    } else {
      sections.claimChart += '<p class="text-muted">No detailed claim chart data available in database.</p>';
    }
  } else {
    sections.products += '<p class="text-muted">No potentially infringing products found in database.</p>';
    sections.claimChart += '<p class="text-muted">No claim chart analysis found in database.</p>';
  }

  // Add any additional analysis data if available
  if (patentData.additional_data) {
    let additionalText = '';
    if (typeof patentData.additional_data === 'string') {
      additionalText = patentData.additional_data;
    } else if (typeof patentData.additional_data === 'object' && patentData.additional_data !== null) {
      try {
        additionalText = JSON.stringify(patentData.additional_data, null, 2);
      } catch (e) {
        additionalText = 'Additional data available but could not be formatted';
      }
    }
    if (additionalText && additionalText.trim() !== '') {
      sections.summary += `## Additional Analysis Data\n\n${additionalText}\n\n`;
    }
  }

  // Handle case where no meaningful data is found or add summary from infringed models
  if (!patentData.analysis_data && !patentData.novelty_data &&
      (!patentData.infringed_models || patentData.infringed_models.length === 0)) {
    sections.summary += `## Database Entry Found\n\nA database entry exists for patent ${patentNumber}, but it contains limited analysis data. You may want to run a new analysis to get more comprehensive results.\n\n`;
  } else if (!patentData.analysis_data && patentData.infringed_models && patentData.infringed_models.length > 0) {
    // Generate summary from infringed models if no analysis data
    sections.summary += `## Analysis Summary\n\nPatent ${patentNumber} analysis found ${patentData.infringed_models.length} potentially infringing product(s):\n\n`;

    patentData.infringed_models.forEach((model, index) => {
      sections.summary += `**${index + 1}. ${model.company || 'Unknown Company'} - ${model.model || model.product_name || 'Unknown Model'}**\n`;
      sections.summary += `- Risk Level: ${model.infringement_risk || model.risk || 'Not assessed'}\n`;
      if (model.risk_justification || model.justification) {
        sections.summary += `- Justification: ${model.risk_justification || model.justification}\n`;
      }
      sections.summary += '\n';
    });
  }

  // If summary is still empty, add a basic summary
  if (sections.summary.trim() === `# Patent Analysis Summary: ${patentNumber}`) {
    sections.summary += `## Database Analysis\n\nPatent ${patentNumber} was found in the database with the following information:\n\n`;

    if (patentData.infringed_models && patentData.infringed_models.length > 0) {
      sections.summary += `- **${patentData.infringed_models.length} potentially infringing product(s)** identified\n`;
    }

    if (patentData.novelty_data) {
      sections.summary += `- **Novelty analysis** data available\n`;
    }

    if (patentData.analysis_data) {
      sections.summary += `- **Analysis data** available\n`;
    }

    sections.summary += `\nFor detailed information, check the other tabs or run a new analysis for updated results.\n\n`;
  }

  // Add metadata
  const metadata = `---\n\n*Analysis retrieved from database on: ${new Date().toLocaleString()}*\n*Patent Number: ${patentNumber}*\n`;
  Object.keys(sections).forEach(key => {
    sections[key] += metadata;
  });

  // Clean up any [object Object] references before displaying
  Object.keys(sections).forEach(key => {
    sections[key] = sections[key].replace(/\[object Object\]/g, 'Data available (format needs processing)');
  });

  // Update all tab content in the UI
  summaryContent.innerHTML = marked ? marked.parse(sections.summary) : sections.summary.replace(/\n/g, '<br>');
  noveltyContent.innerHTML = marked ? marked.parse(sections.novelty) : sections.novelty.replace(/\n/g, '<br>');
  productsContent.innerHTML = sections.products;
  claimChartContent.innerHTML = sections.claimChart;

  // Show the results container
  resultsContainer.classList.remove('d-none');

  console.log('✅ Existing patent data processed and displayed');
}

// Legacy function removed - using the main startAnalysis function above

// Show/hide loading indicator
function showLoading(show) {
  if (show) {
    loadingIndicator.classList.remove('d-none');
    analyzeBtn.disabled = true;
    analyzeBtn.textContent = 'Analyzing...';
  } else {
    loadingIndicator.classList.add('d-none');
    analyzeBtn.disabled = false;
    analyzeBtn.textContent = 'Analyze';
  }
}

// Update loading text
function updateLoadingText(text) {
  const loadingText = document.getElementById('loading-text');
  if (loadingText) {
    loadingText.textContent = text;
  }
}

// Create a new session
async function createSession() {
  try {
    console.log('Creating session:', currentSessionId);

    const response = await fetch(`${API_BASE_URL}/apps/${APP_NAME}/users/${currentUserId}/sessions/${currentSessionId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({})
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Session creation error:', errorText);
      throw new Error(`Failed to create session: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ Session created:', data);
    return data;
  } catch (error) {
    console.error('Error creating session:', error);
    throw new Error(`Session creation failed: ${error.message}`);
  }
}

// Send a message to the API
async function sendMessage(message) {
  try {
    console.log('Sending message:', message);

    const response = await fetch(`${API_BASE_URL}/run`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        app_name: APP_NAME,
        user_id: currentUserId,
        session_id: currentSessionId,
        new_message: {
          parts: [{ text: message }],
          role: 'user'
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API error response:', errorText);
      throw new Error(`API request failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ API response received');
    return data;
  } catch (error) {
    console.error('Error sending message:', error);
    throw new Error(`Failed to send analysis request: ${error.message}`);
  }
}

// Process the API response and update the UI
async function processResults(response, patentNumber) {
  console.log('Processing results for patent:', patentNumber);

  // Extract and format content using the enhanced function
  const sections = extractAndFormatContent(response, patentNumber);

  // Update all tab content in the UI with enhanced formatting
  summaryContent.innerHTML = marked ? marked.parse(sections.summary) : sections.summary.replace(/\n/g, '<br>');
  noveltyContent.innerHTML = marked ? marked.parse(sections.novelty) : sections.novelty.replace(/\n/g, '<br>');

  // Apply special formatting for products and claim charts
  const formattedProductsHTML = formatPotentiallyInfringingProducts(sections.products);
  productsContent.innerHTML = marked ? marked.parse(formattedProductsHTML) : formattedProductsHTML.replace(/\n/g, '<br>');

  const formattedClaimChartHTML = enhanceTableStyling(sections.claimChart, 'claim-chart-table');
  claimChartContent.innerHTML = marked ? marked.parse(formattedClaimChartHTML) : formattedClaimChartHTML.replace(/\n/g, '<br>');

  // Show the results container
  resultsContainer.classList.remove('d-none');

  console.log('✅ Results processed and displayed');
}

// Show alert messages
function showAlert(message, type = 'info') {
  // Clear existing alerts
  clearAlerts();

  const alertDiv = document.createElement('div');
  alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
  alertDiv.innerHTML = `
    ${message}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;

  alertContainer.appendChild(alertDiv);

  // Auto-dismiss after 5 seconds for success/info alerts
  if (type === 'success' || type === 'info') {
    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 5000);
  }
}

// Clear all alerts
function clearAlerts() {
  alertContainer.innerHTML = '';
}



// Utility function to format dates
function formatDate(dateString) {
  const options = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  };
  return new Date(dateString).toLocaleDateString('en-US', options);
}

// Utility function to debounce input
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Add keyboard shortcuts
document.addEventListener('keydown', (e) => {
  // Ctrl+Enter to analyze
  if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
    e.preventDefault();
    if (!analyzeBtn.disabled) {
      startAnalysis();
    }
  }

  // Escape to clear input
  if (e.key === 'Escape') {
    patentInput.value = '';
    patentInput.focus();
  }
});

// Handle connection errors
window.addEventListener('online', () => {
  showAlert('Connection restored', 'success');
});

window.addEventListener('offline', () => {
  showAlert('Connection lost. Please check your internet connection.', 'warning');
});

// Add error handling for unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
  showAlert('An unexpected error occurred. Please try again.', 'danger');
});

// Performance monitoring
const performanceObserver = new PerformanceObserver((list) => {
  const entries = list.getEntries();
  entries.forEach((entry) => {
    if (entry.duration > 1000) { // Log slow operations
      console.warn(`Slow operation detected: ${entry.name} took ${entry.duration}ms`);
    }
  });
});

// Start observing performance
try {
  performanceObserver.observe({ entryTypes: ['measure', 'navigation'] });
} catch (e) {
  console.log('Performance observer not supported');
}

// Initialize app state
function initializeApp() {
  console.log('🚀 Patent Infringement Analyzer initialized');
  console.log(`User ID: ${currentUserId}`);
  console.log(`API Base URL: ${API_BASE_URL}`);
  
  // Focus on patent input
  patentInput.focus();
  
  // Check server connectivity
  checkServerConnection();
}

// Check server connectivity
async function checkServerConnection() {
  try {
    // Check the main API server
    const response = await fetch(`${API_BASE_URL}/apps`, {
      method: 'GET'
    });

    if (response.ok) {
      console.log('✅ Main API server connection established');
    } else {
      console.warn('⚠️ Main API server connection check failed');
      // Removed warning alert as it's not useful when server detection is unreliable
    }
  } catch (error) {
    console.warn('⚠️ Main API server connection check failed:', error.message);
    // Removed warning alert as it's not useful when server detection is unreliable
  }
}

// Call initialization when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeApp);
} else {
  initializeApp();
}