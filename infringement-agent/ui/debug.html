<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - Patent Infringement Analyzer</title>
</head>
<body>
    <h1>Debug Page</h1>
    <button id="test-btn">Test Button</button>
    <div id="output"></div>

    <script>
        console.log('Debug script loaded');
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM loaded');
            
            const testBtn = document.getElementById('test-btn');
            const output = document.getElementById('output');
            
            if (testBtn) {
                console.log('Test button found');
                testBtn.addEventListener('click', () => {
                    console.log('Test button clicked');
                    output.innerHTML = 'Button works!';
                });
            } else {
                console.error('Test button not found');
            }
            
            // Test if the main script elements exist
            const analyzeBtn = document.getElementById('analyze-btn');
            const patentInput = document.getElementById('patent-input');
            
            console.log('Analyze button exists:', !!analyzeBtn);
            console.log('Patent input exists:', !!patentInput);
            
            // Try to load the main script
            const script = document.createElement('script');
            script.src = 'script.js';
            script.onload = () => {
                console.log('Main script loaded successfully');
                output.innerHTML += '<br>Main script loaded successfully';
            };
            script.onerror = (error) => {
                console.error('Main script failed to load:', error);
                output.innerHTML += '<br>Main script failed to load: ' + error;
            };
            document.head.appendChild(script);
        });
    </script>
</body>
</html>
