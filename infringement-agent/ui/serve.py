#!/usr/bin/env python3
"""
Simple HTTP server for the Patent Infringement Analyzer UI.
This allows you to serve the UI files locally.
"""

import http.server
import socketserver
import os
import webbrowser
import base64
import json
import sys
from urllib.parse import urlparse

# Add the parent directory to the path to import database utilities
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
try:
    from tools.database_utils import get_complete_patent_analysis, init_db
except ImportError:
    print("Warning: Database utilities not available. Database features will be disabled.")
    def get_complete_patent_analysis(patent_number):
        return None
    def init_db():
        pass

# Configuration
PORT = 8080
DIRECTORY = os.path.dirname(os.path.abspath(__file__))


class Handler(http.server.SimpleHTTPRequestHandler):
    """Custom request handler with CORS support"""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)

    def do_GET(self):
        if self.path == '/download-report':
            self.handle_excel_download()
        else:
            # For all other requests, use the default handler
            super().do_GET()

    def handle_excel_download(self):
        """Generate and serve Excel report from database data"""
        try:
            # Get the latest patent analysis from database
            latest_patent = self.get_latest_patent_from_db()

            if not latest_patent:
                self.send_error(404, 'No patent analysis found in database')
                return

            # Generate Excel file
            excel_file_path = self.generate_excel_report(latest_patent)

            if excel_file_path and os.path.exists(excel_file_path):
                # Set headers for file download
                self.send_response(200)
                self.send_header('Content-type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                self.send_header('Content-Disposition', f'attachment; filename="{os.path.basename(excel_file_path)}"')
                self.end_headers()

                # Send the file content
                with open(excel_file_path, 'rb') as f:
                    self.wfile.write(f.read())
            else:
                self.send_error(500, 'Failed to generate Excel report')

        except Exception as e:
            print(f"Error generating Excel report: {e}")
            self.send_error(500, f'Error generating Excel report: {str(e)}')

    def do_POST(self):
        print(f"POST request received for path: {self.path}")
        if self.path == '/api/check-patent':
            self.handle_check_patent()
        elif self.path == '/api/get-patent':
            self.handle_get_patent()
        elif self.path == '/api/debug-db':
            self.handle_debug_db()
        else:
            print(f"Unknown API endpoint: {self.path}")
            self.send_error(404, 'API endpoint not found')

    def handle_check_patent(self):
        try:
            print("Handling check-patent request")
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            patent_number = data.get('patent_number')
            print(f"Checking patent: {patent_number}")

            if not patent_number:
                self.send_error(400, 'Patent number required')
                return

            # Check if patent exists in database
            try:
                patent_data = get_complete_patent_analysis(patent_number)
                print(f"Raw patent data: {patent_data}")
                exists = bool(patent_data and patent_data.get('has_data', False))
                print(f"Patent {patent_number} exists in database: {exists}")
                if patent_data:
                    print(f"Patent data keys: {list(patent_data.keys())}")
                    print(f"Has data flag: {patent_data.get('has_data', False)}")
                    print(f"Analysis data type: {type(patent_data.get('analysis_data'))}")
                    print(f"Infringed models count: {len(patent_data.get('infringed_models', []))}")
            except Exception as db_error:
                print(f"Database error: {db_error}")
                exists = False

            response = {
                'exists': exists,
                'patent_number': patent_number
            }

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode('utf-8'))
            print(f"Response sent: {response}")

        except Exception as e:
            print(f"Error checking patent: {e}")
            self.send_error(500, f'Internal server error: {str(e)}')

    def handle_get_patent(self):
        try:
            print("Handling get-patent request")
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))

            patent_number = data.get('patent_number')
            print(f"Getting patent data for: {patent_number}")

            if not patent_number:
                self.send_error(400, 'Patent number required')
                return

            # Get complete patent data from database
            try:
                patent_data = get_complete_patent_analysis(patent_number)
                print(f"Patent data retrieved: {bool(patent_data)}")
                if patent_data:
                    print(f"Patent data keys: {list(patent_data.keys())}")
                    print(f"Analysis data: {patent_data.get('analysis_data', 'None')[:200] if patent_data.get('analysis_data') else 'None'}...")
                    print(f"Novelty data type: {type(patent_data.get('novelty_data'))}")
                    if patent_data.get('novelty_data'):
                        print(f"Novelty data content: {str(patent_data.get('novelty_data'))[:200]}...")
                    print(f"Infringed models: {len(patent_data.get('infringed_models', []))} items")
            except Exception as db_error:
                print(f"Database error: {db_error}")
                patent_data = None

            if not patent_data:
                self.send_error(404, 'Patent not found in database')
                return

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(patent_data, default=str).encode('utf-8'))
            print("Patent data sent successfully")

        except Exception as e:
            print(f"Error getting patent: {e}")
            self.send_error(500, f'Internal server error: {str(e)}')

    def handle_debug_db(self):
        """Debug endpoint to check database contents"""
        try:
            import sqlite3
            import os

            # Get database path
            db_path = os.path.join(os.path.dirname(__file__), '..', 'wissen_core.db')
            print(f"Checking database at: {db_path}")
            print(f"Database exists: {os.path.exists(db_path)}")

            if not os.path.exists(db_path):
                response = {"error": "Database file not found", "path": db_path}
            else:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()

                # Get all tables
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()

                response = {
                    "database_path": db_path,
                    "tables": [table[0] for table in tables],
                    "patent_analysis_count": 0,
                    "patent_novelty_count": 0,
                    "infringed_models_count": 0
                }

                # Count records in each table
                try:
                    cursor.execute("SELECT COUNT(*) FROM patent_analysis")
                    response["patent_analysis_count"] = cursor.fetchone()[0]
                except:
                    pass

                try:
                    cursor.execute("SELECT COUNT(*) FROM patent_novelty")
                    response["patent_novelty_count"] = cursor.fetchone()[0]
                except:
                    pass

                try:
                    cursor.execute("SELECT COUNT(*) FROM infringed_models")
                    response["infringed_models_count"] = cursor.fetchone()[0]
                except:
                    pass

                # Get sample patent numbers
                try:
                    cursor.execute("SELECT DISTINCT patent_number FROM patent_analysis LIMIT 5")
                    response["sample_patents"] = [row[0] for row in cursor.fetchall()]
                except:
                    response["sample_patents"] = []

                conn.close()

            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response, indent=2).encode('utf-8'))

        except Exception as e:
            print(f"Error in debug handler: {e}")
            self.send_error(500, f'Debug error: {str(e)}')

    def get_latest_report(self, directory):
        """Get the latest (most recently modified) report from a directory."""
        try:
            # Get all files in the directory
            files = [os.path.join(directory, f) for f in os.listdir(directory) if f.endswith('.xlsx')]

            # Return the most recently modified file
            if files:
                return max(files, key=os.path.getmtime)
        except FileNotFoundError:
            return None
        return None

    def get_latest_patent_from_db(self):
        """Get the latest patent analysis from database"""
        try:
            import sqlite3
            db_path = os.path.join(os.path.dirname(__file__), '..', 'wissen_core.db')

            if not os.path.exists(db_path):
                return None

            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # First try to get from patent_analysis table
            cursor.execute("""
                SELECT patent_number FROM patent_analysis
                ORDER BY created_at DESC LIMIT 1
            """)

            result = cursor.fetchone()

            # If no patent_analysis records, get from infringed_models table
            if not result:
                cursor.execute("""
                    SELECT DISTINCT patent_number FROM infringed_models
                    ORDER BY inserted_at DESC LIMIT 1
                """)
                result = cursor.fetchone()

            conn.close()

            if result:
                return result[0]
            return None

        except Exception as e:
            print(f"Error getting latest patent: {e}")
            return None

    def generate_excel_report(self, patent_number):
        """Generate Excel report from database data"""
        try:
            # Import the Excel generation tool
            sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
            from tools.database_utils import get_complete_patent_analysis
            from tools.claim_chart_to_excel import create_excel_from_claim_charts

            # Get complete patent data from database
            patent_data = get_complete_patent_analysis(patent_number)

            if not patent_data:
                print(f"No data found for patent {patent_number}")
                return None

            # Create the generated_reports directory if it doesn't exist
            reports_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'generated_reports')
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            # Generate filename
            safe_patent_number = patent_number.replace('/', '_').replace('\\', '_')
            filename = f"{safe_patent_number}_Claim_Charts.xlsx"
            output_path = os.path.join(reports_dir, filename)

            # Convert patent data to the format expected by the Excel tool
            excel_data = self.convert_patent_data_to_excel_format(patent_data)

            # Generate Excel file
            result = create_excel_from_claim_charts(excel_data, filename)

            if "successfully" in result:
                return output_path
            else:
                print(f"Excel generation failed: {result}")
                return None

        except Exception as e:
            print(f"Error generating Excel report: {e}")
            return None

    def convert_patent_data_to_excel_format(self, patent_data):
        """Convert database patent data to format expected by Excel tool"""
        try:
            # Extract infringed models data
            models_data = []

            # patent_data is a dictionary returned by get_complete_patent_analysis
            if isinstance(patent_data, dict) and 'infringed_models' in patent_data:
                infringed_models = patent_data['infringed_models']
            else:
                print("No infringed models found in patent data")
                print(f"Patent data keys: {list(patent_data.keys()) if isinstance(patent_data, dict) else 'Not a dict'}")
                return "[]"

            for model in infringed_models:
                # Models from database are dictionaries
                model_dict = {
                    "company": model.get('company', 'Unknown'),
                    "model": model.get('model', 'Unknown'),
                    "category": model.get('category', 'Unknown'),
                    "activity_date": model.get('launched_date', ''),
                    "risk_assessment": model.get('infringement_risk', 'Not assessed'),
                    "risk_justification": model.get('risk_justification', 'Not provided'),
                    "claim_chart": []
                }

                # Add claim charts if available
                claim_charts = model.get('claim_charts', [])
                if claim_charts:
                    for chart in claim_charts:
                        chart_dict = {
                            "claim_element": chart.get('claim_element', ''),
                            "corresponding_feature": chart.get('corresponding_feature', ''),
                            "source_justification": chart.get('source_justification', '')
                        }
                        model_dict["claim_chart"].append(chart_dict)

                models_data.append(model_dict)

            # Convert to JSON string as expected by the Excel tool
            import json
            return json.dumps(models_data, indent=2)

        except Exception as e:
            print(f"Error converting patent data: {e}")
            return "[]"

    def do_OPTIONS(self):
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

def main():
    """Start the server and open the browser"""
    print(f"Serving at http://localhost:{PORT}")

    # Initialize database
    try:
        init_db()
        print("Database initialized successfully")
    except Exception as e:
        print(f"Warning: Database initialization failed: {e}")

    # Create the server
    with socketserver.TCPServer(("", PORT), Handler) as httpd:
        print("Server started. Press Ctrl+C to stop.")
        
        # Open the browser
        webbrowser.open(f"http://localhost:{PORT}")
        
        # Serve until interrupted
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    main()
